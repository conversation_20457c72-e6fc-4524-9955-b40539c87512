import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Plus, Loader2, X, Trash2, DollarSign } from "lucide-react";
import {
  selectReferrerLinks,
  selectReferrerLinksLoading,
  selectReferrerLinksError,
  selectReferrerLinksPagination,
} from "@/features/selectors";
import { referrerLinksActions } from "@/features/rootActions";
import { ReferrerLink } from "@/features/referrer-links/referrer-links.slice";
import TableLinks from "@/components/TableLinks";
import LinkModal, { LinkFormData } from "@/components/Modals/LinkModal";
import Conversions from "./Conversions";

// Interface for pagination
interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

const DeleteConfirmation: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  linkName: string;
  isLoading?: boolean;
}> = ({ isOpen, onClose, onConfirm, linkName, isLoading = false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md overflow-hidden transform transition-all">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Delete Link
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          <div className="flex items-center gap-4 mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-900/30">
            <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-full">
              <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <p className="text-sm text-red-600 dark:text-red-400 font-medium">
              This action cannot be undone
            </p>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Are you sure you want to delete{" "}
            <span className="font-semibold text-gray-900 dark:text-white">
              "{linkName}"
            </span>
            ?
          </p>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-650 border border-gray-300 dark:border-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center justify-center min-w-[80px] transition-colors shadow-sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

const ReferralsContainer: React.FC = () => {
  const dispatch = useDispatch();

  // Links selectors
  const links = useSelector(selectReferrerLinks);
  const isLoadingLinks = useSelector(selectReferrerLinksLoading);
  const linksError = useSelector(selectReferrerLinksError);
  const linksPagination = useSelector(selectReferrerLinksPagination);

  const [currentLinksPage, setCurrentLinksPage] = useState(1);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [copiedType, setCopiedType] = useState<'page' | 'shortLink' | 'url' | null>(null);

  // Add modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentLink, setCurrentLink] = useState<ReferrerLink | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(
      referrerLinksActions.fetchLinks({ page: currentLinksPage, pageSize: 25 })
    );
  }, [dispatch, currentLinksPage]);

  // Track previous loading state to detect when operations complete
  const [prevLoadingState, setPrevLoadingState] = useState(false);

  // Close modals when loading changes from true to false (operation completed)
  useEffect(() => {
    // If loading was true and now it's false, and there's no error, close modals
    if (prevLoadingState && !isLoadingLinks && !linksError) {
      if (isCreateModalOpen || isEditModalOpen) {
        setIsCreateModalOpen(false);
        setIsEditModalOpen(false);
      }
    }
    // Update previous loading state
    setPrevLoadingState(isLoadingLinks);
  }, [
    isLoadingLinks,
    linksError,
    isCreateModalOpen,
    isEditModalOpen,
    prevLoadingState,
  ]);

  const handleLinksPageChange = (pageNum: number) => {
    setCurrentLinksPage(pageNum);
    // Scroll to top of table when changing pages
    document
      .querySelector(".overflow-x-auto")
      ?.scrollIntoView({ behavior: "smooth" });
  };

  const copyToClipboard = async (text: string, id: string, type: 'page' | 'shortLink' | 'url') => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setCopiedType(type);

      // Reset copied status after 2.5 seconds to allow users to see the success message
      setTimeout(() => {
        setCopiedId(null);
        setCopiedType(null);
      }, 2500);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopiedId(id);
        setCopiedType(type);
        setTimeout(() => {
          setCopiedId(null);
          setCopiedType(null);
        }, 2500);
      } catch (fallbackErr) {
        console.error('Fallback copy failed: ', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  const handleClearError = () => {
    dispatch(referrerLinksActions.clearError());
  };

  const handleCreateLink = (data: LinkFormData) => {
    dispatch(referrerLinksActions.createLink(data));
    // Modal will stay open and show loading state until success/error
  };

  const handleEditLink = (data: LinkFormData) => {
    if (!currentLink || !currentLink.documentId) return;

    dispatch(
      referrerLinksActions.updateLink({
        id: currentLink.id,
        documentId: currentLink.documentId,
        name: data.name,
        url: data.url,
        shortLink: data.shortLink,
        selectedPage: data.selectedPage,
      })
    );
    // Modal will stay open and show loading state until success/error
  };

  const handleDeleteLink = () => {
    if (!currentLink || !currentLink.documentId) return;

    dispatch(
      referrerLinksActions.deleteLink({
        id: currentLink.id,
        documentId: currentLink.documentId,
      })
    );
    setIsDeleteModalOpen(false);
  };

  const handleEdit = (link: ReferrerLink) => {
    setCurrentLink(link);
    setIsEditModalOpen(true);
  };

  const handleDelete = (link: ReferrerLink) => {
    setCurrentLink(link);
    setIsDeleteModalOpen(true);
  };

  const handleCreateNewLink = () => {
    setIsCreateModalOpen(true);
  };

  // Helper function to parse URL into path and viaValue
  const parseUrl = (url: string): { path: string; viaValue: string } => {
    try {
      const urlObj = new URL(url);
      // Extract path without leading slash
      const path = urlObj.pathname.replace(/^\//, "");
      // Extract via parameter
      const viaValue = urlObj.searchParams.get("via") || "";

      return { path, viaValue };
    } catch (error) {
      // If URL parsing fails, return default values
      console.error("Error parsing URL:", error);
      return { path: "", viaValue: "" };
    }
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
      {/* Page Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-lg sm:text-xl">📋</span>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
            Referrals & Links
          </h1>
        </div>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Manage your affiliate links and track referral performance.
        </p>
      </div>

      {/* Your Affiliate Links Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 gap-4">
          <h2 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white">
            Your Affiliate Links
          </h2>
          <button
            onClick={handleCreateNewLink}
            className="bg-blue-600 hover:bg-blue-700 text-white py-3 sm:py-2 px-4 rounded-md text-sm flex items-center justify-center transition-colors min-h-[44px] sm:min-h-[36px] w-full sm:w-auto"
            disabled={isLoadingLinks}
          >
            <Plus className="w-4 h-4 mr-1.5" />
            Create New Link
          </button>
        </div>

        {linksError && (
          <div className="mx-6 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {linksError}
          </div>
        )}

        <TableLinks
          links={links}
          isLoading={isLoadingLinks}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onCopy={copyToClipboard}
          copiedId={copiedId}
          copiedType={copiedType}
          enablePagination={true}
          pagination={linksPagination}
          currentPage={currentLinksPage}
          onPageChange={handleLinksPageChange}
          isPaginationLoading={isLoadingLinks && currentLinksPage !== 1}
        />
      </div>

      {/* Referral Activity Section - Display separately */}
      <Conversions />

      {/* Add the modals - Now using the shared component */}
      <LinkModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateLink}
        title="Create New Link"
        isLoading={isLoadingLinks}
        apiError={linksError}
        onClearError={handleClearError}
      />

      <LinkModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleEditLink}
        initialData={
          currentLink
            ? (() => {
                const { path, viaValue } = parseUrl(currentLink.url);
                return {
                  name: currentLink.name,
                  path: path,
                  viaValue: viaValue,
                  shortLink: currentLink.short_link || "",
                  selectedPage: currentLink.page?.documentId || "",
                  url: currentLink.url,
                };
              })()
            : undefined
        }
        title="Edit Link"
        isLoading={isLoadingLinks}
        apiError={linksError}
        onClearError={handleClearError}
      />

      <DeleteConfirmation
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteLink}
        linkName={currentLink?.name || ""}
        isLoading={isLoadingLinks}
      />
    </div>
  );
};

export default ReferralsContainer;
